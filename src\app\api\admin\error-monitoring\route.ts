import { NextRequest, NextResponse } from 'next/server';
import { errorHandlingSystem } from '@/lib/error-handling';

/**
 * Error Monitoring API
 * Provides error metrics, reports, and alert management
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'metrics';
    const timeRange = searchParams.get('timeRange') || '24h';

    switch (action) {
      case 'metrics':
        return await getErrorMetrics(timeRange);
      
      case 'alerts':
        return await getActiveAlerts();
      
      case 'report':
        return await generateErrorReport(timeRange);
      
      case 'health':
        return await getSystemHealth();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: metrics, alerts, report, or health' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Error monitoring API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (!action) {
      return NextResponse.json(
        { error: 'Action parameter required' },
        { status: 400 }
      );
    }

    const body = await request.json();

    switch (action) {
      case 'acknowledge-alert':
        return await acknowledgeAlert(body.alertId);
      
      case 'resolve-alert':
        return await resolveAlert(body.alertId);
      
      case 'test-error':
        return await testErrorHandling(body);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Error monitoring API POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * Get current error metrics
 */
async function getErrorMetrics(timeRange: string) {
  const errorMetrics = errorHandlingSystem.errorManager.getErrorMetrics();
  const activeAlerts = errorHandlingSystem.errorManager.getActiveAlerts();

  // Convert Map to object for JSON serialization
  const metricsObject: Record<string, any> = {};
  for (const [key, value] of errorMetrics.entries()) {
    metricsObject[key] = {
      ...value,
      lastOccurrence: value.lastOccurrence?.toISOString()
    };
  }

  return NextResponse.json({
    success: true,
    data: {
      timeRange,
      metrics: metricsObject,
      summary: {
        totalErrorTypes: errorMetrics.size,
        activeAlerts: activeAlerts.length,
        criticalAlerts: activeAlerts.filter(a => a.severity === 'critical').length,
        highAlerts: activeAlerts.filter(a => a.severity === 'high').length
      },
      timestamp: new Date().toISOString()
    }
  });
}

/**
 * Get active alerts
 */
async function getActiveAlerts() {
  const activeAlerts = errorHandlingSystem.errorManager.getActiveAlerts();

  return NextResponse.json({
    success: true,
    data: {
      alerts: activeAlerts,
      summary: {
        total: activeAlerts.length,
        critical: activeAlerts.filter(a => a.severity === 'critical').length,
        high: activeAlerts.filter(a => a.severity === 'high').length,
        medium: activeAlerts.filter(a => a.severity === 'medium').length,
        unacknowledged: activeAlerts.filter(a => !a.acknowledged).length
      },
      timestamp: new Date().toISOString()
    }
  });
}

/**
 * Generate error report for specified time range
 */
async function generateErrorReport(timeRange: string) {
  // Parse time range
  const timeRangeObj = parseTimeRange(timeRange);
  
  const report = await errorHandlingSystem.errorManager.generateErrorReport(timeRangeObj);

  return NextResponse.json({
    success: true,
    data: {
      report,
      timestamp: new Date().toISOString()
    }
  });
}

/**
 * Get system health status
 */
async function getSystemHealth() {
  const healthStatus = await errorHandlingSystem.healthChecker.runAllHealthChecks();

  return NextResponse.json({
    success: true,
    data: {
      health: healthStatus,
      timestamp: new Date().toISOString()
    }
  });
}

/**
 * Acknowledge an alert
 */
async function acknowledgeAlert(alertId: string) {
  if (!alertId) {
    return NextResponse.json(
      { error: 'Alert ID required' },
      { status: 400 }
    );
  }

  const success = errorHandlingSystem.errorManager.acknowledgeAlert(alertId);

  if (success) {
    return NextResponse.json({
      success: true,
      message: 'Alert acknowledged successfully'
    });
  } else {
    return NextResponse.json(
      { error: 'Alert not found' },
      { status: 404 }
    );
  }
}

/**
 * Resolve an alert
 */
async function resolveAlert(alertId: string) {
  if (!alertId) {
    return NextResponse.json(
      { error: 'Alert ID required' },
      { status: 400 }
    );
  }

  const success = errorHandlingSystem.errorManager.resolveAlert(alertId);

  if (success) {
    return NextResponse.json({
      success: true,
      message: 'Alert resolved successfully'
    });
  } else {
    return NextResponse.json(
      { error: 'Alert not found' },
      { status: 404 }
    );
  }
}

/**
 * Test error handling system
 */
async function testErrorHandling(testConfig: any) {
  try {
    const { errorType, context } = testConfig;

    // Create a test error
    const testError = new Error(`Test error: ${errorType}`);
    (testError as any).code = errorType;

    // Handle the error using the error management system
    const result = await errorHandlingSystem.errorManager.handleError(testError, context || {});

    return NextResponse.json({
      success: true,
      data: {
        testError: {
          type: errorType,
          message: testError.message
        },
        recoveryResult: result,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: 'Test failed', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * Parse time range string into TimeRange object
 */
function parseTimeRange(timeRange: string): { start: Date; end: Date } {
  const end = new Date();
  let start: Date;

  switch (timeRange) {
    case '1h':
      start = new Date(end.getTime() - 60 * 60 * 1000);
      break;
    case '24h':
      start = new Date(end.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      start = new Date(end.getTime() - 24 * 60 * 60 * 1000); // Default to 24h
  }

  return { start, end };
}
