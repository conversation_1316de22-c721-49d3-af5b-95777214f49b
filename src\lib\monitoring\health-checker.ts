import {
  HealthCheck,
  HealthCheckResult,
  SystemHealthStatus
} from '../error-handling/types';

/**
 * System Health Checker
 * Monitors the health of all system components
 */
export class HealthChecker {
  private healthChecks = new Map<string, HealthCheck>();
  private lastResults = new Map<string, HealthCheckResult>();
  private isRunning = false;

  constructor() {
    this.initializeHealthChecks();
  }

  /**
   * Initialize all health checks
   */
  private initializeHealthChecks(): void {
    // Database connectivity check
    this.healthChecks.set('database', {
      name: 'Database Connectivity',
      check: this.checkDatabaseHealth.bind(this),
      interval: 30000, // 30 seconds
      timeout: 5000,   // 5 seconds
      retries: 3,
      critical: true
    });

    // Scrape.do API health check
    this.healthChecks.set('scrape_do_api', {
      name: 'Scrape.do API',
      check: this.checkScrapeDoHealth.bind(this),
      interval: 60000, // 1 minute
      timeout: 10000,  // 10 seconds
      retries: 2,
      critical: false
    });

    // OpenAI API health check
    this.healthChecks.set('openai_api', {
      name: 'OpenAI API',
      check: this.checkOpenAIHealth.bind(this),
      interval: 120000, // 2 minutes
      timeout: 15000,   // 15 seconds
      retries: 2,
      critical: false
    });

    // OpenRouter API health check
    this.healthChecks.set('openrouter_api', {
      name: 'OpenRouter API',
      check: this.checkOpenRouterHealth.bind(this),
      interval: 120000, // 2 minutes
      timeout: 15000,   // 15 seconds
      retries: 2,
      critical: false
    });

    // Job queue health check
    this.healthChecks.set('job_queue', {
      name: 'Job Queue',
      check: this.checkJobQueueHealth.bind(this),
      interval: 45000, // 45 seconds
      timeout: 5000,   // 5 seconds
      retries: 1,
      critical: true
    });

    // Memory usage check
    this.healthChecks.set('memory_usage', {
      name: 'Memory Usage',
      check: this.checkMemoryUsage.bind(this),
      interval: 60000, // 1 minute
      timeout: 1000,   // 1 second
      retries: 1,
      critical: false
    });

    // Disk space check
    this.healthChecks.set('disk_space', {
      name: 'Disk Space',
      check: this.checkDiskSpace.bind(this),
      interval: 300000, // 5 minutes
      timeout: 2000,    // 2 seconds
      retries: 1,
      critical: false
    });
  }

  /**
   * Run all health checks
   */
  async runAllHealthChecks(): Promise<SystemHealthStatus> {
    const results = new Map<string, HealthCheckResult>();

    for (const [name, healthCheck] of this.healthChecks) {
      try {
        const result = await this.runHealthCheckWithRetry(healthCheck);
        results.set(name, result);
        this.lastResults.set(name, result);
      } catch (error: any) {
        const errorResult: HealthCheckResult = {
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date(),
          consecutiveFailures: this.incrementConsecutiveFailures(name)
        };
        results.set(name, errorResult);
        this.lastResults.set(name, errorResult);
      }
    }

    return this.calculateOverallHealth(results);
  }

  /**
   * Run a single health check with retry logic
   */
  private async runHealthCheckWithRetry(healthCheck: HealthCheck): Promise<HealthCheckResult> {
    let lastError: Error;

    for (let attempt = 1; attempt <= healthCheck.retries + 1; attempt++) {
      try {
        const result = await this.executeHealthCheckWithTimeout(healthCheck);
        
        // Reset consecutive failures on success
        this.resetConsecutiveFailures(healthCheck.name);
        
        return result;
      } catch (error: any) {
        lastError = error;
        
        if (attempt <= healthCheck.retries) {
          // Wait before retry
          await this.delay(1000 * attempt);
        }
      }
    }

    throw lastError!;
  }

  /**
   * Execute health check with timeout
   */
  private async executeHealthCheckWithTimeout(healthCheck: HealthCheck): Promise<HealthCheckResult> {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Health check timeout after ${healthCheck.timeout}ms`));
      }, healthCheck.timeout);

      try {
        const result = await healthCheck.check();
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<HealthCheckResult> {
    try {
      const startTime = Date.now();
      
      // Simple database connectivity test
      // In a real implementation, this would use the actual database client
      const response = await fetch('/api/health/database', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          status: 'healthy',
          responseTime,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      } else {
        return {
          status: 'unhealthy',
          error: `Database check failed with status ${response.status}`,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Check Scrape.do API health
   */
  private async checkScrapeDoHealth(): Promise<HealthCheckResult> {
    try {
      const startTime = Date.now();
      
      // Test scrape.do API availability
      const response = await fetch('https://api.scrape.do/health', {
        method: 'GET',
        headers: {
          'X-API-KEY': process.env.SCRAPE_DO_API_KEY || ''
        }
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          status: 'healthy',
          responseTime,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      } else {
        return {
          status: response.status === 429 ? 'degraded' : 'unhealthy',
          error: `Scrape.do API returned status ${response.status}`,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Check OpenAI API health
   */
  private async checkOpenAIHealth(): Promise<HealthCheckResult> {
    try {
      const startTime = Date.now();
      
      // Test OpenAI API with minimal request
      const response = await fetch('https://api.openai.com/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          status: 'healthy',
          responseTime,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      } else {
        return {
          status: response.status === 429 ? 'degraded' : 'unhealthy',
          error: `OpenAI API returned status ${response.status}`,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Check OpenRouter API health
   */
  private async checkOpenRouterHealth(): Promise<HealthCheckResult> {
    try {
      const startTime = Date.now();
      
      // Test OpenRouter API with minimal request
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          status: 'healthy',
          responseTime,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      } else {
        return {
          status: response.status === 429 ? 'degraded' : 'unhealthy',
          error: `OpenRouter API returned status ${response.status}`,
          timestamp: new Date(),
          details: { statusCode: response.status }
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Check job queue health
   */
  private async checkJobQueueHealth(): Promise<HealthCheckResult> {
    try {
      // Check job queue statistics
      // In a real implementation, this would check the actual job queue
      const response = await fetch('/api/health/jobs', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const stats = await response.json();
        const failureRate = stats.failed / (stats.total || 1);
        
        let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
        if (failureRate > 0.5) status = 'unhealthy';
        else if (failureRate > 0.1) status = 'degraded';

        return {
          status,
          timestamp: new Date(),
          details: stats
        };
      } else {
        return {
          status: 'unhealthy',
          error: `Job queue check failed with status ${response.status}`,
          timestamp: new Date()
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Check memory usage
   */
  private async checkMemoryUsage(): Promise<HealthCheckResult> {
    try {
      const memoryUsage = process.memoryUsage();
      const usedMemoryMB = memoryUsage.heapUsed / 1024 / 1024;
      const totalMemoryMB = memoryUsage.heapTotal / 1024 / 1024;
      const usagePercentage = (usedMemoryMB / totalMemoryMB) * 100;

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (usagePercentage > 90) status = 'unhealthy';
      else if (usagePercentage > 75) status = 'degraded';

      return {
        status,
        timestamp: new Date(),
        details: {
          usedMemoryMB: Math.round(usedMemoryMB),
          totalMemoryMB: Math.round(totalMemoryMB),
          usagePercentage: Math.round(usagePercentage)
        }
      };
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Check disk space
   */
  private async checkDiskSpace(): Promise<HealthCheckResult> {
    try {
      // Simplified disk space check
      // In a real implementation, this would check actual disk usage
      return {
        status: 'healthy',
        timestamp: new Date(),
        details: {
          diskUsagePercentage: 45, // Placeholder
          availableSpaceGB: 100    // Placeholder
        }
      };
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Calculate overall system health
   */
  private calculateOverallHealth(results: Map<string, HealthCheckResult>): SystemHealthStatus {
    const statuses = Array.from(results.values()).map(r => r.status);
    const criticalChecks = Array.from(this.healthChecks.values()).filter(c => c.critical);
    
    // Check if any critical systems are unhealthy
    const criticalUnhealthy = criticalChecks.some(check => {
      const result = results.get(check.name);
      return result?.status === 'unhealthy';
    });

    const unhealthyCount = statuses.filter(s => s === 'unhealthy').length;
    const degradedCount = statuses.filter(s => s === 'degraded').length;
    const healthyCount = statuses.filter(s => s === 'healthy').length;

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    
    if (criticalUnhealthy || unhealthyCount > 0) {
      overallStatus = 'unhealthy';
    } else if (degradedCount > 0) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'healthy';
    }

    const criticalIssues = Array.from(results.entries())
      .filter(([name, result]) => {
        const check = this.healthChecks.get(name);
        return check?.critical && result.status === 'unhealthy';
      })
      .map(([name]) => name);

    const recommendations = this.generateHealthRecommendations(results);

    return {
      overall: overallStatus,
      checks: Object.fromEntries(results),
      timestamp: new Date(),
      summary: {
        total: results.size,
        healthy: healthyCount,
        degraded: degradedCount,
        unhealthy: unhealthyCount
      },
      criticalIssues,
      recommendations
    };
  }

  /**
   * Generate health recommendations
   */
  private generateHealthRecommendations(results: Map<string, HealthCheckResult>): string[] {
    const recommendations: string[] = [];

    for (const [name, result] of results) {
      if (result.status === 'unhealthy') {
        switch (name) {
          case 'database':
            recommendations.push('Check database connection and server status');
            break;
          case 'openai_api':
            recommendations.push('Verify OpenAI API key and quota status');
            break;
          case 'openrouter_api':
            recommendations.push('Check OpenRouter API credits and service status');
            break;
          case 'scrape_do_api':
            recommendations.push('Verify Scrape.do API key and service availability');
            break;
          case 'job_queue':
            recommendations.push('Investigate job queue failures and worker status');
            break;
          case 'memory_usage':
            recommendations.push('Consider restarting the application or scaling resources');
            break;
        }
      }
    }

    return recommendations;
  }

  /**
   * Utility methods
   */
  private incrementConsecutiveFailures(checkName: string): number {
    const lastResult = this.lastResults.get(checkName);
    return (lastResult?.consecutiveFailures || 0) + 1;
  }

  private resetConsecutiveFailures(checkName: string): void {
    const lastResult = this.lastResults.get(checkName);
    if (lastResult) {
      lastResult.consecutiveFailures = 0;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Start continuous health monitoring
   */
  startMonitoring(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('Health monitoring started');

    // Run initial health check
    this.runAllHealthChecks();

    // Schedule periodic health checks
    for (const [name, healthCheck] of this.healthChecks) {
      setInterval(async () => {
        try {
          const result = await this.runHealthCheckWithRetry(healthCheck);
          this.lastResults.set(name, result);
        } catch (error: any) {
          const errorResult: HealthCheckResult = {
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date(),
            consecutiveFailures: this.incrementConsecutiveFailures(name)
          };
          this.lastResults.set(name, errorResult);
        }
      }, healthCheck.interval);
    }
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring(): void {
    this.isRunning = false;
    console.log('Health monitoring stopped');
  }

  /**
   * Get last health check results
   */
  getLastResults(): Map<string, HealthCheckResult> {
    return new Map(this.lastResults);
  }

  /**
   * Get health check for specific component
   */
  async checkComponentHealth(componentName: string): Promise<HealthCheckResult | null> {
    const healthCheck = this.healthChecks.get(componentName);
    if (!healthCheck) return null;

    try {
      return await this.runHealthCheckWithRetry(healthCheck);
    } catch (error: any) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }
}
