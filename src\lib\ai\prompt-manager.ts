import { MultiPromptContext } from './types';

export class PromptManager {
  
  /**
   * System prompts for different content generation scenarios
   */
  static readonly SYSTEM_PROMPTS = {
    toolAnalysis: `You are an expert AI tool analyst with deep knowledge of the AI industry. Your task is to analyze AI tools and generate comprehensive, accurate content that matches our database schema.

CRITICAL REQUIREMENTS:
- Generate content in JSON format matching the exact database schema
- Use an irreverent, humorous tone similar to ThePornDude style
- Be factual but entertaining in descriptions
- Include specific technical details and use cases
- Generate 3-8 features, 3-10 pros/cons, 3-6 Q&As
- Keep descriptions under 500 characters, detailed descriptions 150-300 words
- Create engaging haikus and relevant hashtags
- Classify pricing accurately: Free, Paid, Freemium, or Open Source

SCHEMA REQUIREMENTS:
{
  "detailed_description": "string (150-300 words)",
  "features": ["array of 3-8 feature strings"],
  "pricing": {
    "type": "Free|Paid|Freemium|Open Source",
    "plans": [{"name": "string", "price": "string", "features": ["array"]}]
  },
  "pros_and_cons": {
    "pros": ["array of 3-10 pros"],
    "cons": ["array of 3-10 cons"]
  },
  "haiku": {
    "lines": ["line1", "line2", "line3"],
    "theme": "string"
  },
  "hashtags": ["array of 5-10 relevant hashtags"],
  "social_links": {
    "twitter": "url or null",
    "linkedin": "url or null",
    "github": "url or null"
  }
}`,

    contentCompletion: `You are continuing content generation for an AI tool. Previous context has been provided. Generate the remaining content sections while maintaining consistency with the established tone and style.

CONTINUATION REQUIREMENTS:
- Maintain the same irreverent, humorous tone
- Ensure consistency with previously generated content
- Complete any missing sections from the schema
- Do not repeat information from previous chunks
- Focus on the specific sections requested`,

    contentValidation: `You are a content quality validator. Review the generated content for accuracy, completeness, and adherence to our style guidelines.

VALIDATION CRITERIA:
- Schema compliance (all required fields present)
- Content quality (engaging, informative, accurate)
- Tone consistency (irreverent but professional)
- Technical accuracy (features and capabilities)
- Pricing accuracy (correct classification and details)`
  };

  /**
   * Build a single prompt for complete content generation
   */
  static buildSinglePrompt(
    scrapedContent: string,
    toolUrl: string,
    options: {
      includeSchema?: boolean;
      customInstructions?: string;
    } = {}
  ): string {
    const { includeSchema = true, customInstructions = '' } = options;

    let prompt = `${this.SYSTEM_PROMPTS.toolAnalysis}

Tool URL: ${toolUrl}
Scraped Content:
${scrapedContent}

${customInstructions}

Generate comprehensive content for this AI tool following the exact JSON schema requirements above. Ensure all required fields are included and maintain the irreverent, engaging tone throughout.`;

    return prompt;
  }

  /**
   * Build multi-prompt for large content processing
   */
  static buildMultiPrompt(
    chunk: string,
    context: MultiPromptContext,
    options: {
      customInstructions?: string;
    } = {}
  ): string {
    const { 
      isFirstChunk, 
      isLastChunk, 
      chunkIndex, 
      totalChunks, 
      accumulatedContext, 
      toolUrl 
    } = context;
    
    const { customInstructions = '' } = options;
    
    let prompt = '';
    
    if (isFirstChunk) {
      prompt = `${this.SYSTEM_PROMPTS.toolAnalysis}

MULTI-CHUNK PROCESSING: This is chunk ${chunkIndex + 1} of ${totalChunks}.
Generate initial content sections and prepare for continuation.

Tool URL: ${toolUrl}
Scraped Content Chunk:
${chunk}

${customInstructions}

Generate the following sections for this chunk:
- detailed_description (if sufficient information available)
- features (partial list, will be completed in subsequent chunks)
- pricing (if pricing information is in this chunk)

Respond with JSON and include a "continuation_needed" field indicating what sections need completion.`;

    } else if (isLastChunk) {
      prompt = `${this.SYSTEM_PROMPTS.contentCompletion}

FINAL CHUNK: Complete all remaining sections and finalize the content.

Previous Context:
${accumulatedContext}

Final Content Chunk:
${chunk}

${customInstructions}

Complete the content generation with all remaining sections:
- Complete features list
- pros_and_cons
- haiku
- hashtags
- social_links
- Any missing sections

Provide the final complete JSON response.`;

    } else {
      prompt = `${this.SYSTEM_PROMPTS.contentCompletion}

MIDDLE CHUNK: Continue content generation with this additional information.

Previous Context:
${accumulatedContext}

Additional Content Chunk:
${chunk}

${customInstructions}

Continue building the content, focusing on:
- Additional features
- More detailed information
- Pricing details (if found)

Respond with JSON and indicate what sections still need completion.`;
    }
    
    return prompt;
  }

  /**
   * Build validation prompt for content quality checking
   */
  static buildValidationPrompt(
    generatedContent: any,
    originalContent: string,
    toolUrl: string
  ): string {
    return `${this.SYSTEM_PROMPTS.contentValidation}

Original Tool URL: ${toolUrl}
Original Scraped Content: ${originalContent.substring(0, 1000)}...

Generated Content to Validate:
${JSON.stringify(generatedContent, null, 2)}

Please validate this content and provide:
1. Overall quality score (0-100)
2. List of any issues found
3. Suggestions for improvement
4. Compliance with schema requirements

Respond with JSON format:
{
  "quality_score": number,
  "issues": ["array of issues"],
  "suggestions": ["array of suggestions"],
  "schema_compliance": boolean,
  "tone_consistency": boolean,
  "technical_accuracy": boolean
}`;
  }

  /**
   * Build prompt for content enhancement/refinement
   */
  static buildEnhancementPrompt(
    existingContent: any,
    enhancementType: 'features' | 'description' | 'pricing' | 'pros_cons',
    additionalContext?: string
  ): string {
    return `You are enhancing existing AI tool content. Focus on improving the ${enhancementType} section while maintaining consistency with the existing content.

Existing Content:
${JSON.stringify(existingContent, null, 2)}

${additionalContext ? `Additional Context:\n${additionalContext}\n` : ''}

Enhancement Instructions:
${this.getEnhancementInstructions(enhancementType)}

Provide the enhanced content in JSON format, including only the sections that have been improved.`;
  }

  private static getEnhancementInstructions(type: string): string {
    const instructions = {
      features: `Enhance the features list by:
- Adding more specific technical details
- Including use cases and benefits
- Ensuring 3-8 comprehensive features
- Making each feature clear and actionable`,

      description: `Enhance the detailed description by:
- Adding more engaging and humorous elements
- Including specific technical capabilities
- Maintaining 150-300 word limit
- Improving readability and flow`,

      pricing: `Enhance the pricing information by:
- Clarifying pricing tiers and features
- Adding value propositions for each plan
- Ensuring accurate pricing classification
- Including any free trial or freemium details`,

      pros_cons: `Enhance the pros and cons by:
- Adding more specific and actionable points
- Balancing positive and negative aspects
- Including user experience insights
- Ensuring 3-10 items in each category`
    };

    return instructions[type as keyof typeof instructions] || 'Enhance the content for better quality and accuracy.';
  }

  /**
   * Extract and clean JSON from AI response
   */
  static extractJsonFromResponse(response: string): any {
    try {
      // Remove markdown code blocks if present
      let cleanContent = response.trim();
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      return JSON.parse(cleanContent);
    } catch (error) {
      throw new Error(`Failed to parse AI response as JSON: ${error}. Content: ${response.substring(0, 200)}...`);
    }
  }

  /**
   * Combine results from multiple prompt processing
   */
  static combineMultiPromptResults(results: any[]): any {
    if (results.length === 1) {
      return results[0];
    }

    // Combine results from multiple chunks
    const combined: any = {};
    
    for (const result of results) {
      if (typeof result === 'object' && result !== null) {
        Object.assign(combined, result);
        
        // Special handling for arrays - concatenate unique items
        if (result.features && Array.isArray(result.features)) {
          combined.features = [...new Set([...(combined.features || []), ...result.features])];
        }
        
        if (result.hashtags && Array.isArray(result.hashtags)) {
          combined.hashtags = [...new Set([...(combined.hashtags || []), ...result.hashtags])];
        }
      }
    }

    return combined;
  }

  /**
   * Calculate token count for prompt optimization
   */
  static calculateTokenCount(text: string): number {
    // Approximate token calculation (1 token ≈ 4 characters for English)
    return Math.ceil(text.length / 4);
  }

  /**
   * Optimize prompt length for model constraints
   */
  static optimizePromptLength(
    prompt: string, 
    maxTokens: number, 
    preserveSections: string[] = ['CRITICAL REQUIREMENTS', 'SCHEMA REQUIREMENTS']
  ): string {
    const currentTokens = this.calculateTokenCount(prompt);
    
    if (currentTokens <= maxTokens) {
      return prompt;
    }

    // If prompt is too long, try to preserve critical sections
    const lines = prompt.split('\n');
    const preservedLines: string[] = [];
    const optionalLines: string[] = [];

    let inPreservedSection = false;
    
    for (const line of lines) {
      const isPreservedSection = preserveSections.some(section => line.includes(section));
      
      if (isPreservedSection) {
        inPreservedSection = true;
      }
      
      if (inPreservedSection || isPreservedSection) {
        preservedLines.push(line);
      } else {
        optionalLines.push(line);
      }
      
      if (line.trim() === '' && inPreservedSection) {
        inPreservedSection = false;
      }
    }

    // Start with preserved content and add optional content until limit
    let optimizedPrompt = preservedLines.join('\n');
    
    for (const line of optionalLines) {
      const testPrompt = optimizedPrompt + '\n' + line;
      if (this.calculateTokenCount(testPrompt) <= maxTokens) {
        optimizedPrompt = testPrompt;
      } else {
        break;
      }
    }

    return optimizedPrompt;
  }
}
