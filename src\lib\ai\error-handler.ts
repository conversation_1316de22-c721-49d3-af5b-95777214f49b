import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ider, AIModel } from './types';

export class AIErrorHandler {
  
  /**
   * Handle AI provider errors with specific error classification and recovery suggestions
   */
  static async handleAIError(error: any, context: ErrorContext): Promise<ErrorResult> {
    const errorResult: ErrorResult = {
      success: false,
      error: error.message || 'Unknown AI error',
      retryable: false,
      context,
      timestamp: new Date().toISOString()
    };

    // OpenAI specific errors
    if (context.provider === 'openai') {
      this.handleOpenAIErrors(error, errorResult);
    }
    
    // OpenRouter specific errors
    if (context.provider === 'openrouter') {
      this.handleOpenRouterErrors(error, errorResult);
    }

    // General AI errors
    this.handleGeneralAIErrors(error, errorResult);

    return errorResult;
  }

  /**
   * Handle OpenAI-specific errors
   */
  private static handleOpenAIErrors(error: any, errorResult: ErrorResult): void {
    if (error.code === 'rate_limit_exceeded') {
      errorRes<PERSON>.retryable = true;
      errorResult.retryAfter = 60; // Wait 1 minute
      errorResult.suggestion = 'Switch to OpenRouter or implement exponential backoff';
    } else if (error.code === 'context_length_exceeded') {
      errorResult.suggestion = 'Split content into smaller chunks or use Gemini 2.5 Pro with larger context window';
    } else if (error.code === 'invalid_request_error') {
      errorResult.error = 'Invalid request format or parameters';
      errorResult.suggestion = 'Check request format and model parameters';
    } else if (error.code === 'insufficient_quota') {
      errorResult.error = 'OpenAI quota exceeded';
      errorResult.suggestion = 'Check OpenAI billing and quota limits, or switch to OpenRouter';
    } else if (error.code === 'model_not_found') {
      errorResult.error = 'Requested model not available';
      errorResult.suggestion = 'Use a different model or check model availability';
    } else if (error.code === 'server_error') {
      errorResult.retryable = true;
      errorResult.retryAfter = 30;
      errorResult.suggestion = 'OpenAI server error - retry or switch to OpenRouter';
    }
  }

  /**
   * Handle OpenRouter-specific errors
   */
  private static handleOpenRouterErrors(error: any, errorResult: ErrorResult): void {
    if (error.status === 402) {
      errorResult.error = 'Insufficient credits on OpenRouter';
      errorResult.suggestion = 'Check OpenRouter balance or switch to OpenAI';
    } else if (error.status === 429) {
      errorResult.retryable = true;
      errorResult.retryAfter = 30;
      errorResult.suggestion = 'OpenRouter rate limit - wait and retry or switch to OpenAI';
    } else if (error.status === 400) {
      errorResult.error = 'Bad request to OpenRouter';
      errorResult.suggestion = 'Check request format and model parameters';
    } else if (error.status === 401) {
      errorResult.error = 'OpenRouter authentication failed';
      errorResult.suggestion = 'Check OpenRouter API key';
    } else if (error.status === 503) {
      errorResult.retryable = true;
      errorResult.retryAfter = 60;
      errorResult.error = 'OpenRouter service unavailable';
      errorResult.suggestion = 'OpenRouter service temporarily unavailable - retry or switch to OpenAI';
    } else if (error.status === 422) {
      errorResult.error = 'OpenRouter validation error';
      errorResult.suggestion = 'Check model parameters and request format';
    }
  }

  /**
   * Handle general AI errors
   */
  private static handleGeneralAIErrors(error: any, errorResult: ErrorResult): void {
    // Content validation errors
    if (error.type === 'validation_error') {
      errorResult.suggestion = 'Review system prompt and content requirements';
    }

    // Network errors
    if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
      errorResult.retryable = true;
      errorResult.retryAfter = 30;
      errorResult.suggestion = 'Network connectivity issue - check internet connection';
    }

    // Timeout errors
    if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
      errorResult.retryable = true;
      errorResult.retryAfter = 60;
      errorResult.suggestion = 'Request timeout - try with smaller content or increase timeout';
    }

    // JSON parsing errors
    if (error.message.includes('JSON') || error.message.includes('parse')) {
      errorResult.suggestion = 'AI response format error - adjust system prompt or try different model';
    }
  }

  /**
   * Retry operation with exponential backoff and provider fallback
   */
  static async retryWithFallback<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    context: ErrorContext = {}
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        const errorResult = await this.handleAIError(error, { 
          ...context, 
          attempt, 
          maxRetries 
        });

        console.warn(`Attempt ${attempt}/${maxRetries} failed:`, {
          error: errorResult.error,
          suggestion: errorResult.suggestion,
          retryable: errorResult.retryable
        });

        if (!errorResult.retryable || attempt === maxRetries) {
          break;
        }

        // Exponential backoff with jitter
        const baseDelay = errorResult.retryAfter ? errorResult.retryAfter * 1000 : 1000;
        const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
        const jitter = Math.random() * 1000; // Add up to 1 second of jitter
        const delay = Math.min(exponentialDelay + jitter, 60000); // Cap at 1 minute

        console.log(`Waiting ${Math.round(delay)}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Determine if error is recoverable with different provider
   */
  static isProviderSwitchRecommended(error: any, currentProvider: AIProvider): boolean {
    // Switch providers for these error types
    const switchRecommendedErrors = [
      'rate_limit_exceeded',
      'insufficient_quota',
      'service_unavailable',
      'server_error'
    ];

    // Check error codes
    if (switchRecommendedErrors.includes(error.code)) {
      return true;
    }

    // Check HTTP status codes for OpenRouter
    if (currentProvider === 'openrouter' && [402, 503, 429].includes(error.status)) {
      return true;
    }

    // Check for quota/billing issues
    if (error.message && (
      error.message.includes('quota') ||
      error.message.includes('billing') ||
      error.message.includes('credits')
    )) {
      return true;
    }

    return false;
  }

  /**
   * Get error recovery recommendations
   */
  static getRecoveryRecommendations(error: any, context: ErrorContext): string[] {
    const recommendations: string[] = [];

    // Provider-specific recommendations
    if (context.provider === 'openai') {
      if (error.code === 'context_length_exceeded') {
        recommendations.push('Switch to OpenRouter with Gemini 2.5 Pro (1M+ token context)');
        recommendations.push('Split content into smaller chunks');
      }
      if (error.code === 'rate_limit_exceeded') {
        recommendations.push('Switch to OpenRouter for better rate limits');
        recommendations.push('Implement request queuing with delays');
      }
    }

    if (context.provider === 'openrouter') {
      if (error.status === 402) {
        recommendations.push('Add credits to OpenRouter account');
        recommendations.push('Switch to OpenAI temporarily');
      }
      if (error.status === 503) {
        recommendations.push('Switch to OpenAI while OpenRouter recovers');
        recommendations.push('Retry with exponential backoff');
      }
    }

    // General recommendations
    if (error.message && error.message.includes('timeout')) {
      recommendations.push('Reduce content size for faster processing');
      recommendations.push('Increase timeout settings');
    }

    if (error.message && error.message.includes('JSON')) {
      recommendations.push('Adjust system prompt for better JSON formatting');
      recommendations.push('Add JSON schema validation');
    }

    return recommendations;
  }

  /**
   * Log error with context for monitoring
   */
  static logError(error: any, context: ErrorContext): void {
    const logData = {
      timestamp: new Date().toISOString(),
      provider: context.provider,
      model: context.model,
      operation: context.operation,
      attempt: context.attempt,
      maxRetries: context.maxRetries,
      error: {
        message: error.message,
        code: error.code,
        status: error.status,
        type: error.type
      }
    };

    console.error('AI Error:', JSON.stringify(logData, null, 2));

    // In production, you might want to send this to a monitoring service
    // like Sentry, DataDog, or CloudWatch
  }

  /**
   * Check if error indicates a permanent failure
   */
  static isPermanentFailure(error: any): boolean {
    const permanentErrors = [
      'invalid_api_key',
      'model_not_found',
      'invalid_request_error'
    ];

    return permanentErrors.includes(error.code) || 
           (error.status && [401, 403, 404].includes(error.status));
  }

  /**
   * Get suggested wait time before retry
   */
  static getSuggestedWaitTime(error: any, attempt: number): number {
    // If error specifies retry-after, use that
    if (error.headers && error.headers['retry-after']) {
      return parseInt(error.headers['retry-after']) * 1000;
    }

    // Rate limit errors - longer wait
    if (error.code === 'rate_limit_exceeded' || error.status === 429) {
      return Math.min(60000 * attempt, 300000); // 1-5 minutes
    }

    // Server errors - shorter wait
    if (error.status >= 500) {
      return Math.min(5000 * attempt, 60000); // 5-60 seconds
    }

    // Default exponential backoff
    return Math.min(1000 * Math.pow(2, attempt - 1), 30000); // 1-30 seconds
  }
}
