import OpenAI from 'openai';
import { 
  AIResponse, 
  AIModelConfig, 
  OPENAI_CONFIG,
  MODEL_OUTPUT_LIMITS 
} from '../types';

export class OpenAIClient {
  private client: OpenAI;
  private config: AIModelConfig;

  constructor(config?: Partial<AIModelConfig>) {
    this.config = { ...OPENAI_CONFIG, ...config };
    
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: 'https://api.openai.com/v1',
      timeout: this.config.timeout,
    });
  }

  async generateContent(
    systemPrompt: string,
    userPrompt: string,
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      responseFormat?: 'json_object' | 'json_schema';
      jsonSchema?: any;
    } = {}
  ): Promise<AIResponse> {
    const model = options.model || this.config.model;
    const temperature = options.temperature ?? this.config.temperature;
    const maxTokens = options.maxTokens || this.getOptimalOutputTokens(model);

    try {
      const requestConfig: any = {
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature,
        max_tokens: maxTokens,
      };

      // Configure response format
      if (options.responseFormat === 'json_schema' && options.jsonSchema) {
        // OpenAI Structured Outputs (strict mode)
        requestConfig.response_format = {
          type: 'json_schema',
          json_schema: {
            name: 'ai_tool_content',
            strict: true,
            schema: options.jsonSchema
          }
        };
      } else if (options.responseFormat === 'json_object') {
        // Standard JSON mode
        requestConfig.response_format = { type: 'json_object' };
      }

      const response = await this.client.chat.completions.create(requestConfig);

      const choice = response.choices[0];
      if (!choice?.message?.content) {
        throw new Error('No content generated from OpenAI');
      }

      return {
        content: choice.message.content,
        tokenUsage: response.usage ? {
          prompt_tokens: response.usage.prompt_tokens,
          completion_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens
        } : undefined,
        model: response.model,
        finishReason: choice.finish_reason || 'unknown'
      };

    } catch (error: any) {
      // Enhanced error handling for OpenAI-specific errors
      if (error.code === 'rate_limit_exceeded') {
        throw new Error(`OpenAI rate limit exceeded: ${error.message}`);
      } else if (error.code === 'context_length_exceeded') {
        throw new Error(`OpenAI context length exceeded: ${error.message}`);
      } else if (error.code === 'invalid_request_error') {
        throw new Error(`OpenAI invalid request: ${error.message}`);
      } else if (error.code === 'insufficient_quota') {
        throw new Error(`OpenAI insufficient quota: ${error.message}`);
      }

      throw new Error(`OpenAI API error: ${error.message}`);
    }
  }

  private getOptimalOutputTokens(model: string): number {
    const limit = MODEL_OUTPUT_LIMITS[model] || MODEL_OUTPUT_LIMITS['openai/gpt-4o'];
    // Use 80% of the limit to leave room for response formatting
    return Math.floor(limit * 0.8);
  }

  async validateConnection(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: 'gpt-4o-mini', // Use cheaper model for validation
        messages: [{ role: 'user', content: 'Test connection' }],
        max_tokens: 10,
        temperature: 0
      });

      return response.choices.length > 0;
    } catch (error) {
      console.error('OpenAI connection validation failed:', error);
      return false;
    }
  }

  getConfig(): AIModelConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<AIModelConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  calculateTokenCount(text: string): number {
    // Approximate token calculation (1 token ≈ 4 characters for English)
    return Math.ceil(text.length / 4);
  }

  canHandleContent(contentSize: number): boolean {
    return contentSize <= this.config.maxInputTokens;
  }

  splitContentForModel(content: string): string[] {
    const tokenCount = this.calculateTokenCount(content);
    const maxTokensPerChunk = Math.floor(this.config.maxInputTokens * 0.8); // Reserve 20% for response
    
    if (tokenCount <= maxTokensPerChunk) {
      return [content];
    }
    
    // Split content by logical sections
    const sections = content.split(/\n(?=#{1,3}\s)/); // Split on headers
    const chunks = [];
    let currentChunk = '';
    
    for (const section of sections) {
      const sectionTokens = this.calculateTokenCount(section);
      const currentTokens = this.calculateTokenCount(currentChunk);
      
      if (currentTokens + sectionTokens > maxTokensPerChunk && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = section;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + section;
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks;
  }

  getProviderInfo(): { provider: string; model: string; capabilities: string[] } {
    return {
      provider: 'OpenAI',
      model: this.config.model,
      capabilities: [
        'Structured Outputs',
        'JSON Mode',
        'Function Calling',
        'High Speed Processing',
        'Reliable Performance'
      ]
    };
  }
}
