/**
 * File Processors for Bulk Processing Engine
 * Handles text file and JSON file processing for bulk URL operations
 */

import { BulkProcessingJob, BulkToolData } from '@/lib/types';

// File processing interfaces
export interface FileProcessingResult {
  success: boolean;
  data?: ProcessedFileData;
  error?: string;
  validation?: ValidationResult;
}

export interface ProcessedFileData {
  type: 'text' | 'json';
  totalItems: number;
  validItems: BulkToolData[];
  invalidItems: InvalidItem[];
  duplicatesRemoved: number;
  processingReady: boolean;
}

export interface InvalidItem {
  index: number;
  content: string;
  reason: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stats: {
    totalLines: number;
    validUrls: number;
    invalidUrls: number;
    comments: number;
    emptyLines: number;
  };
}

export interface JSONFormat {
  type: 'simple' | 'detailed' | 'mapping';
  structure: any;
  description: string;
}

// File specifications
export const FILE_SPECS = {
  text: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedExtensions: ['.txt'],
    encoding: 'UTF-8',
    maxUrls: 1000,
  },
  json: {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedExtensions: ['.json'],
    maxItems: 1000,
  },
} as const;

// URL validation patterns
const URL_PATTERN = /^https?:\/\/[^\s]+$/;
const COMMENT_PATTERN = /^#.*$/;

/**
 * Text File Processor
 * Processes plain text files containing URLs (one per line)
 */
export class TextFileProcessor {
  async processFile(file: File): Promise<FileProcessingResult> {
    try {
      // Validate file specifications
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        return {
          success: false,
          error: `File validation failed: ${validation.errors.join(', ')}`,
          validation,
        };
      }

      // Read file content
      const content = await file.text();
      const lines = content.split(/\r?\n/);

      // Process lines
      const processedData = this.processLines(lines);
      
      return {
        success: true,
        data: {
          type: 'text',
          totalItems: processedData.validUrls.length,
          validItems: processedData.validUrls.map(url => ({
            url,
            providedData: {},
            needsGeneration: {
              name: true,
              description: true,
              features: true,
              pricing: true,
              prosAndCons: true,
              haiku: true,
              hashtags: true,
            },
          })),
          invalidItems: processedData.invalidUrls,
          duplicatesRemoved: processedData.duplicatesRemoved,
          processingReady: processedData.validUrls.length > 0,
        },
        validation: {
          isValid: true,
          errors: [],
          warnings: processedData.invalidUrls.length > 0 ? 
            [`${processedData.invalidUrls.length} invalid URLs were skipped`] : [],
          stats: {
            totalLines: lines.length,
            validUrls: processedData.validUrls.length,
            invalidUrls: processedData.invalidUrls.length,
            comments: processedData.comments,
            emptyLines: processedData.emptyLines,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to process text file: ${(error as Error).message}`,
      };
    }
  }

  private validateFile(file: File): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file size
    if (file.size > FILE_SPECS.text.maxSize) {
      errors.push(`File size (${Math.round(file.size / 1024 / 1024)}MB) exceeds maximum (${FILE_SPECS.text.maxSize / 1024 / 1024}MB)`);
    }

    // Check file extension
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!FILE_SPECS.text.allowedExtensions.includes(extension)) {
      errors.push(`File extension ${extension} not allowed. Allowed: ${FILE_SPECS.text.allowedExtensions.join(', ')}`);
    }

    // Check file name
    if (!file.name || file.name.trim() === '') {
      warnings.push('File has no name');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      stats: {
        totalLines: 0,
        validUrls: 0,
        invalidUrls: 0,
        comments: 0,
        emptyLines: 0,
      },
    };
  }

  private processLines(lines: string[]): {
    validUrls: string[];
    invalidUrls: InvalidItem[];
    duplicatesRemoved: number;
    comments: number;
    emptyLines: number;
  } {
    const validUrls: string[] = [];
    const invalidUrls: InvalidItem[] = [];
    const seenUrls = new Set<string>();
    let comments = 0;
    let emptyLines = 0;
    let duplicatesRemoved = 0;

    lines.forEach((line, index) => {
      const trimmed = line.trim();

      if (!trimmed) {
        emptyLines++;
        return;
      }

      if (COMMENT_PATTERN.test(trimmed)) {
        comments++;
        return;
      }

      if (!URL_PATTERN.test(trimmed)) {
        invalidUrls.push({
          index: index + 1,
          content: trimmed,
          reason: 'Invalid URL format',
        });
        return;
      }

      try {
        // Validate URL using URL constructor
        const url = new URL(trimmed);
        
        // Check for duplicates
        if (seenUrls.has(url.href)) {
          duplicatesRemoved++;
          return;
        }

        seenUrls.add(url.href);
        validUrls.push(url.href);
      } catch {
        invalidUrls.push({
          index: index + 1,
          content: trimmed,
          reason: 'Invalid URL format',
        });
      }
    });

    return {
      validUrls,
      invalidUrls,
      duplicatesRemoved,
      comments,
      emptyLines,
    };
  }
}

/**
 * JSON File Processor
 * Processes JSON files with tool data and URL mapping
 */
export class JSONFileProcessor {
  async processFile(file: File): Promise<FileProcessingResult> {
    try {
      // Validate file specifications
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        return {
          success: false,
          error: `File validation failed: ${validation.errors.join(', ')}`,
          validation,
        };
      }

      // Read and parse JSON content
      const content = await file.text();
      let data: any;

      try {
        data = JSON.parse(content);
      } catch (parseError) {
        return {
          success: false,
          error: `Invalid JSON format: ${(parseError as Error).message}`,
        };
      }

      // Detect JSON format and process
      const format = this.detectJSONFormat(data);
      const processedData = await this.processJSONData(data, format);

      return {
        success: true,
        data: processedData,
        validation: {
          isValid: true,
          errors: [],
          warnings: processedData.invalidItems.length > 0 ?
            [`${processedData.invalidItems.length} invalid items were skipped`] : [],
          stats: {
            totalLines: processedData.totalItems + processedData.invalidItems.length,
            validUrls: processedData.totalItems,
            invalidUrls: processedData.invalidItems.length,
            comments: 0,
            emptyLines: 0,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to process JSON file: ${(error as Error).message}`,
      };
    }
  }

  private validateFile(file: File): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file size
    if (file.size > FILE_SPECS.json.maxSize) {
      errors.push(`File size (${Math.round(file.size / 1024 / 1024)}MB) exceeds maximum (${FILE_SPECS.json.maxSize / 1024 / 1024}MB)`);
    }

    // Check file extension
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!FILE_SPECS.json.allowedExtensions.includes(extension)) {
      errors.push(`File extension ${extension} not allowed. Allowed: ${FILE_SPECS.json.allowedExtensions.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      stats: {
        totalLines: 0,
        validUrls: 0,
        invalidUrls: 0,
        comments: 0,
        emptyLines: 0,
      },
    };
  }

  private detectJSONFormat(data: any): JSONFormat['type'] {
    if (Array.isArray(data.urls)) {
      return 'simple';
    }
    if (Array.isArray(data.tools)) {
      return 'detailed';
    }
    if (data.fieldMapping && Array.isArray(data.data)) {
      return 'mapping';
    }
    throw new Error('Unknown JSON format. Expected: { urls: [...] }, { tools: [...] }, or { fieldMapping: {...}, data: [...] }');
  }

  private async processJSONData(data: any, format: JSONFormat['type']): Promise<ProcessedFileData> {
    switch (format) {
      case 'simple':
        return this.processSimpleJSON(data);
      case 'detailed':
        return this.processDetailedJSON(data);
      case 'mapping':
        return this.processMappingJSON(data);
      default:
        throw new Error(`Unsupported JSON format: ${format}`);
    }
  }

  private processSimpleJSON(data: any): ProcessedFileData {
    const urls = data.urls as string[];
    const validItems: BulkToolData[] = [];
    const invalidItems: InvalidItem[] = [];
    const seenUrls = new Set<string>();
    let duplicatesRemoved = 0;

    urls.forEach((url, index) => {
      try {
        // Validate URL
        const validatedUrl = new URL(url).href;

        // Check for duplicates
        if (seenUrls.has(validatedUrl)) {
          duplicatesRemoved++;
          return;
        }

        seenUrls.add(validatedUrl);
        validItems.push({
          url: validatedUrl,
          providedData: {},
          needsGeneration: {
            name: true,
            description: true,
            features: true,
            pricing: true,
            prosAndCons: true,
            haiku: true,
            hashtags: true,
          },
        });
      } catch {
        invalidItems.push({
          index,
          content: url,
          reason: 'Invalid URL format',
        });
      }
    });

    return {
      type: 'json',
      totalItems: validItems.length,
      validItems,
      invalidItems,
      duplicatesRemoved,
      processingReady: validItems.length > 0,
    };
  }

  private processDetailedJSON(data: any): ProcessedFileData {
    const tools = data.tools as any[];
    const validItems: BulkToolData[] = [];
    const invalidItems: InvalidItem[] = [];
    const seenUrls = new Set<string>();
    let duplicatesRemoved = 0;

    tools.forEach((tool, index) => {
      try {
        // Validate required fields
        if (!tool.url) {
          invalidItems.push({
            index,
            content: JSON.stringify(tool),
            reason: 'Missing required field: url',
          });
          return;
        }

        // Validate URL
        const validatedUrl = new URL(tool.url).href;

        // Check for duplicates
        if (seenUrls.has(validatedUrl)) {
          duplicatesRemoved++;
          return;
        }

        seenUrls.add(validatedUrl);

        // Process tool data
        validItems.push({
          url: validatedUrl,
          providedData: {
            name: tool.name || null,
            category: tool.category || null,
            description: tool.description || null,
            pricing: tool.pricing || null,
            features: tool.features || null,
          },
          needsGeneration: {
            name: !tool.name,
            description: !tool.description,
            features: !tool.features,
            pricing: !tool.pricing,
            prosAndCons: true, // Always generate
            haiku: true, // Always generate
            hashtags: true, // Always generate
          },
        });
      } catch (error) {
        invalidItems.push({
          index,
          content: JSON.stringify(tool),
          reason: (error as Error).message,
        });
      }
    });

    return {
      type: 'json',
      totalItems: validItems.length,
      validItems,
      invalidItems,
      duplicatesRemoved,
      processingReady: validItems.length > 0,
    };
  }

  private processMappingJSON(data: any): ProcessedFileData {
    // This is a more advanced feature for custom field mapping
    // For now, we'll implement a basic version
    const fieldMapping = data.fieldMapping;
    const items = data.data as any[];

    const validItems: BulkToolData[] = [];
    const invalidItems: InvalidItem[] = [];
    const seenUrls = new Set<string>();
    let duplicatesRemoved = 0;

    items.forEach((item, index) => {
      try {
        // Map fields according to fieldMapping
        const url = item[fieldMapping.url];
        if (!url) {
          invalidItems.push({
            index,
            content: JSON.stringify(item),
            reason: 'Missing URL field',
          });
          return;
        }

        // Validate URL
        const validatedUrl = new URL(url).href;

        // Check for duplicates
        if (seenUrls.has(validatedUrl)) {
          duplicatesRemoved++;
          return;
        }

        seenUrls.add(validatedUrl);

        // Map other fields
        const providedData: any = {};
        const needsGeneration: any = {
          prosAndCons: true,
          haiku: true,
          hashtags: true,
        };

        if (fieldMapping.name && item[fieldMapping.name]) {
          providedData.name = item[fieldMapping.name];
          needsGeneration.name = false;
        } else {
          needsGeneration.name = true;
        }

        if (fieldMapping.description && item[fieldMapping.description]) {
          providedData.description = item[fieldMapping.description];
          needsGeneration.description = false;
        } else {
          needsGeneration.description = true;
        }

        if (fieldMapping.category && item[fieldMapping.category]) {
          providedData.category = item[fieldMapping.category];
        }

        if (fieldMapping.features && item[fieldMapping.features]) {
          providedData.features = item[fieldMapping.features];
          needsGeneration.features = false;
        } else {
          needsGeneration.features = true;
        }

        if (fieldMapping.pricing && item[fieldMapping.pricing]) {
          providedData.pricing = item[fieldMapping.pricing];
          needsGeneration.pricing = false;
        } else {
          needsGeneration.pricing = true;
        }

        validItems.push({
          url: validatedUrl,
          providedData,
          needsGeneration,
        });
      } catch (error) {
        invalidItems.push({
          index,
          content: JSON.stringify(item),
          reason: (error as Error).message,
        });
      }
    });

    return {
      type: 'json',
      totalItems: validItems.length,
      validItems,
      invalidItems,
      duplicatesRemoved,
      processingReady: validItems.length > 0,
    };
  }
}

/**
 * Manual Entry Processor
 * Processes manually entered URLs from textarea input
 */
export class ManualEntryProcessor {
  processInput(input: string): FileProcessingResult {
    try {
      const lines = input.split(/\r?\n/);
      const textProcessor = new TextFileProcessor();
      const processedData = textProcessor['processLines'](lines);

      return {
        success: true,
        data: {
          type: 'text',
          totalItems: processedData.validUrls.length,
          validItems: processedData.validUrls.map(url => ({
            url,
            providedData: {},
            needsGeneration: {
              name: true,
              description: true,
              features: true,
              pricing: true,
              prosAndCons: true,
              haiku: true,
              hashtags: true,
            },
          })),
          invalidItems: processedData.invalidUrls,
          duplicatesRemoved: processedData.duplicatesRemoved,
          processingReady: processedData.validUrls.length > 0,
        },
        validation: {
          isValid: true,
          errors: [],
          warnings: processedData.invalidUrls.length > 0 ?
            [`${processedData.invalidUrls.length} invalid URLs were skipped`] : [],
          stats: {
            totalLines: lines.length,
            validUrls: processedData.validUrls.length,
            invalidUrls: processedData.invalidUrls.length,
            comments: processedData.comments,
            emptyLines: processedData.emptyLines,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to process manual entry: ${(error as Error).message}`,
      };
    }
  }

  validateInput(input: string): ValidationResult {
    const lines = input.split(/\r?\n/);
    const stats = {
      totalLines: lines.length,
      validUrls: 0,
      invalidUrls: 0,
      comments: 0,
      emptyLines: 0,
    };

    const errors: string[] = [];
    const warnings: string[] = [];

    if (input.length > 50000) {
      errors.push('Input exceeds maximum length (50,000 characters)');
    }

    lines.forEach((line) => {
      const trimmed = line.trim();

      if (!trimmed) {
        stats.emptyLines++;
      } else if (COMMENT_PATTERN.test(trimmed)) {
        stats.comments++;
      } else if (URL_PATTERN.test(trimmed)) {
        try {
          new URL(trimmed);
          stats.validUrls++;
        } catch {
          stats.invalidUrls++;
        }
      } else {
        stats.invalidUrls++;
      }
    });

    if (stats.validUrls === 0) {
      errors.push('No valid URLs found');
    }

    if (stats.validUrls > FILE_SPECS.text.maxUrls) {
      errors.push(`Too many URLs (${stats.validUrls}). Maximum allowed: ${FILE_SPECS.text.maxUrls}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      stats,
    };
  }
}
