/**
 * Batch Manager for Bulk Processing
 * Handles intelligent batching, resource management, and cost optimization
 */

import { BulkToolData, BulkProcessingJob } from '@/lib/types';
import { getConfigurationManager } from '@/lib/config';

export interface BatchConfiguration {
  defaultBatchSize: number;
  maxBatchSize: number;
  minBatchSize: number;
  adaptiveSizing: boolean;
  priorityBatching: boolean;
  resourceThrottling: boolean;
}

export interface BatchMetrics {
  averageProcessingTime: number;
  successRate: number;
  errorRate: number;
  costPerItem: number;
  throughput: number; // items per minute
}

export interface BatchStrategy {
  batchSize: number;
  delayBetweenBatches: number;
  maxConcurrentBatches: number;
  retryStrategy: 'immediate' | 'exponential-backoff' | 'linear-backoff';
  failureThreshold: number; // Stop if failure rate exceeds this
}

/**
 * Batch Manager
 * Optimizes batch processing based on performance metrics and resource constraints
 */
export class BatchManager {
  private configManager;
  private batchMetrics = new Map<string, BatchMetrics>();
  private defaultConfig: BatchConfiguration;

  constructor() {
    this.configManager = getConfigurationManager();
    this.defaultConfig = {
      defaultBatchSize: 5,
      maxBatchSize: 20,
      minBatchSize: 1,
      adaptiveSizing: true,
      priorityBatching: true,
      resourceThrottling: true,
    };
  }

  /**
   * Create optimized batches from tool data
   */
  createBatches(
    tools: BulkToolData[],
    jobId: string,
    options: {
      batchSize?: number;
      priority?: 'low' | 'normal' | 'high';
      adaptiveSizing?: boolean;
    } = {}
  ): BulkToolData[][] {
    const config = this.getBatchConfiguration();
    const strategy = this.calculateBatchStrategy(tools, jobId, options);

    // Sort tools by priority if enabled
    let sortedTools = tools;
    if (config.priorityBatching && options.priority) {
      sortedTools = this.sortToolsByPriority(tools, options.priority);
    }

    // Create batches with calculated strategy
    const batches: BulkToolData[][] = [];
    const batchSize = strategy.batchSize;

    for (let i = 0; i < sortedTools.length; i += batchSize) {
      const batch = sortedTools.slice(i, i + batchSize);
      batches.push(batch);
    }

    console.log(`📊 Created ${batches.length} batches with size ${batchSize} for job ${jobId}`);
    return batches;
  }

  /**
   * Calculate optimal batch strategy
   */
  private calculateBatchStrategy(
    tools: BulkToolData[],
    jobId: string,
    options: {
      batchSize?: number;
      priority?: 'low' | 'normal' | 'high';
      adaptiveSizing?: boolean;
    }
  ): BatchStrategy {
    const config = this.getBatchConfiguration();
    const metrics = this.batchMetrics.get(jobId);

    let batchSize = options.batchSize || config.defaultBatchSize;

    // Adaptive sizing based on historical metrics
    if (config.adaptiveSizing && options.adaptiveSizing !== false && metrics) {
      batchSize = this.calculateAdaptiveBatchSize(metrics, config);
    }

    // Adjust for priority
    if (options.priority === 'high') {
      batchSize = Math.min(batchSize * 1.5, config.maxBatchSize);
    } else if (options.priority === 'low') {
      batchSize = Math.max(batchSize * 0.7, config.minBatchSize);
    }

    // Ensure batch size is within bounds
    batchSize = Math.max(config.minBatchSize, Math.min(config.maxBatchSize, Math.round(batchSize)));

    // Calculate delay based on batch size and system load
    const delayBetweenBatches = this.calculateBatchDelay(batchSize, tools.length);

    return {
      batchSize,
      delayBetweenBatches,
      maxConcurrentBatches: this.calculateMaxConcurrentBatches(),
      retryStrategy: 'exponential-backoff',
      failureThreshold: 0.3, // Stop if 30% of items fail
    };
  }

  /**
   * Calculate adaptive batch size based on metrics
   */
  private calculateAdaptiveBatchSize(metrics: BatchMetrics, config: BatchConfiguration): number {
    let adaptedSize = config.defaultBatchSize;

    // Increase batch size if success rate is high and processing time is low
    if (metrics.successRate > 0.9 && metrics.averageProcessingTime < 30000) { // 30 seconds
      adaptedSize = Math.min(config.defaultBatchSize * 1.5, config.maxBatchSize);
    }
    // Decrease batch size if success rate is low or processing time is high
    else if (metrics.successRate < 0.7 || metrics.averageProcessingTime > 120000) { // 2 minutes
      adaptedSize = Math.max(config.defaultBatchSize * 0.5, config.minBatchSize);
    }
    // Adjust based on error rate
    else if (metrics.errorRate > 0.2) {
      adaptedSize = Math.max(config.defaultBatchSize * 0.7, config.minBatchSize);
    }

    return Math.round(adaptedSize);
  }

  /**
   * Calculate delay between batches
   */
  private calculateBatchDelay(batchSize: number, totalItems: number): number {
    const baseDelay = 2000; // 2 seconds base delay
    
    // Increase delay for larger batches to avoid overwhelming APIs
    const sizeMultiplier = Math.max(1, batchSize / 5);
    
    // Increase delay for larger total jobs to spread load over time
    const volumeMultiplier = totalItems > 100 ? 1.5 : 1;
    
    return Math.round(baseDelay * sizeMultiplier * volumeMultiplier);
  }

  /**
   * Calculate maximum concurrent batches
   */
  private calculateMaxConcurrentBatches(): number {
    const config = this.getBatchConfiguration();
    
    if (!config.resourceThrottling) {
      return 5; // Default maximum
    }

    // Get system configuration for concurrent jobs
    const systemConfig = this.configManager.getConfiguration();
    const maxConcurrentJobs = systemConfig?.jobProcessing?.maxConcurrentJobs || 3;
    
    // Reserve some capacity for other jobs
    return Math.max(1, Math.floor(maxConcurrentJobs * 0.7));
  }

  /**
   * Sort tools by priority
   */
  private sortToolsByPriority(tools: BulkToolData[], priority: string): BulkToolData[] {
    // For now, we'll implement a simple sorting strategy
    // In the future, this could be enhanced with more sophisticated prioritization
    return [...tools].sort((a, b) => {
      // Prioritize tools with more provided data (less work needed)
      const aProvidedFields = Object.values(a.providedData).filter(v => v !== null && v !== undefined).length;
      const bProvidedFields = Object.values(b.providedData).filter(v => v !== null && v !== undefined).length;
      
      if (priority === 'high') {
        // For high priority, process tools with less provided data first (more AI generation needed)
        return aProvidedFields - bProvidedFields;
      } else {
        // For normal/low priority, process tools with more provided data first (less work)
        return bProvidedFields - aProvidedFields;
      }
    });
  }

  /**
   * Update batch metrics
   */
  updateBatchMetrics(
    jobId: string,
    batchIndex: number,
    metrics: {
      processingTime: number;
      successCount: number;
      failureCount: number;
      totalItems: number;
    }
  ): void {
    const existing = this.batchMetrics.get(jobId) || {
      averageProcessingTime: 0,
      successRate: 0,
      errorRate: 0,
      costPerItem: 0,
      throughput: 0,
    };

    // Calculate new metrics (simple moving average)
    const totalItems = metrics.totalItems;
    const successRate = metrics.successCount / totalItems;
    const errorRate = metrics.failureCount / totalItems;
    const throughput = totalItems / (metrics.processingTime / 60000); // items per minute

    // Update with weighted average (give more weight to recent batches)
    const weight = 0.3; // 30% weight to new data
    const updated: BatchMetrics = {
      averageProcessingTime: existing.averageProcessingTime * (1 - weight) + metrics.processingTime * weight,
      successRate: existing.successRate * (1 - weight) + successRate * weight,
      errorRate: existing.errorRate * (1 - weight) + errorRate * weight,
      costPerItem: existing.costPerItem, // TODO: Calculate based on API costs
      throughput: existing.throughput * (1 - weight) + throughput * weight,
    };

    this.batchMetrics.set(jobId, updated);
    
    console.log(`📈 Updated metrics for job ${jobId}, batch ${batchIndex}:`, {
      successRate: Math.round(updated.successRate * 100) + '%',
      errorRate: Math.round(updated.errorRate * 100) + '%',
      throughput: Math.round(updated.throughput) + ' items/min',
    });
  }

  /**
   * Get batch metrics for a job
   */
  getBatchMetrics(jobId: string): BatchMetrics | null {
    return this.batchMetrics.get(jobId) || null;
  }

  /**
   * Check if batch should be stopped due to high failure rate
   */
  shouldStopBatch(jobId: string, failureThreshold: number = 0.3): boolean {
    const metrics = this.batchMetrics.get(jobId);
    if (!metrics) return false;

    return metrics.errorRate > failureThreshold;
  }

  /**
   * Get batch configuration
   */
  private getBatchConfiguration(): BatchConfiguration {
    try {
      const systemConfig = this.configManager.getConfiguration();
      const jobConfig = systemConfig?.jobProcessing;

      if (jobConfig) {
        return {
          defaultBatchSize: jobConfig.batchSize || this.defaultConfig.defaultBatchSize,
          maxBatchSize: Math.min(jobConfig.batchSize * 4, 20) || this.defaultConfig.maxBatchSize,
          minBatchSize: 1,
          adaptiveSizing: true,
          priorityBatching: jobConfig.priorityQueues || this.defaultConfig.priorityBatching,
          resourceThrottling: true,
        };
      }
    } catch (error) {
      console.warn('Failed to get batch configuration, using defaults:', error);
    }

    return this.defaultConfig;
  }

  /**
   * Clear metrics for completed job
   */
  clearJobMetrics(jobId: string): void {
    this.batchMetrics.delete(jobId);
  }

  /**
   * Get all active job metrics
   */
  getAllMetrics(): Map<string, BatchMetrics> {
    return new Map(this.batchMetrics);
  }
}

// Singleton instance
let batchManager: BatchManager | null = null;

export function getBatchManager(): BatchManager {
  if (!batchManager) {
    batchManager = new BatchManager();
  }
  return batchManager;
}
