/**
 * Individual Bulk Processing Job API Routes
 * Handles operations on specific bulk processing jobs
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBulkProcessingEngine } from '@/lib/bulk-processing/bulk-engine';
import { getBatchManager } from '@/lib/bulk-processing/batch-manager';

// Admin API key validation
function validateAdminApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
  return apiKey === expectedKey;
}

/**
 * GET /api/admin/bulk-processing/[id]
 * Get specific bulk processing job details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const jobId = params.id;
    const bulkEngine = getBulkProcessingEngine();
    const batchManager = getBatchManager();

    const job = await bulkEngine.getBulkJob(jobId);
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    // Get batch metrics if available
    const metrics = batchManager.getBatchMetrics(jobId);

    return NextResponse.json({
      success: true,
      data: {
        job,
        metrics,
        progress: {
          percentage: job.totalItems > 0 ? Math.round((job.processedItems / job.totalItems) * 100) : 0,
          processed: job.processedItems,
          total: job.totalItems,
          successful: job.successfulItems,
          failed: job.failedItems,
          remaining: job.totalItems - job.processedItems,
        },
      },
    });
  } catch (error) {
    console.error(`Failed to get bulk processing job ${params.id}:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to get job details' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/bulk-processing/[id]
 * Update bulk processing job (pause, resume, cancel)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const jobId = params.id;
    const body = await request.json();
    const { action } = body;

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Missing required field: action' },
        { status: 400 }
      );
    }

    const bulkEngine = getBulkProcessingEngine();

    switch (action) {
      case 'pause':
        await bulkEngine.pauseBulkJob(jobId);
        break;

      case 'resume':
        await bulkEngine.resumeBulkJob(jobId);
        break;

      case 'cancel':
        await bulkEngine.cancelBulkJob(jobId);
        break;

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    // Get updated job details
    const updatedJob = await bulkEngine.getBulkJob(jobId);

    return NextResponse.json({
      success: true,
      data: {
        job: updatedJob,
        message: `Job ${action}d successfully`,
      },
    });
  } catch (error) {
    console.error(`Failed to update bulk processing job ${params.id}:`, error);
    const body = await request.json().catch(() => ({}));
    return NextResponse.json(
      { success: false, error: `Failed to ${body?.action || 'update'} job` },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/bulk-processing/[id]
 * Delete bulk processing job (only if completed or failed)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const jobId = params.id;
    const bulkEngine = getBulkProcessingEngine();
    const batchManager = getBatchManager();

    // Get job to check status
    const job = await bulkEngine.getBulkJob(jobId);
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    // Only allow deletion of completed, failed, or cancelled jobs
    if (!['completed', 'failed', 'cancelled'].includes(job.status)) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete active job. Please cancel it first.' },
        { status: 400 }
      );
    }

    // Delete from database (using Supabase client)
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { error } = await supabase
      .from('bulk_processing_jobs')
      .delete()
      .eq('id', jobId);

    if (error) {
      console.error(`Failed to delete bulk job ${jobId}:`, error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete job from database' },
        { status: 500 }
      );
    }

    // Clear batch metrics
    batchManager.clearJobMetrics(jobId);

    return NextResponse.json({
      success: true,
      data: {
        message: 'Job deleted successfully',
        deletedJobId: jobId,
      },
    });
  } catch (error) {
    console.error(`Failed to delete bulk processing job ${params.id}:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete job' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/bulk-processing/[id]/retry
 * Retry failed items in a bulk processing job
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const jobId = params.id;
    const body = await request.json();
    const { action } = body;

    if (action !== 'retry') {
      return NextResponse.json(
        { success: false, error: 'This endpoint only supports retry action' },
        { status: 400 }
      );
    }

    const bulkEngine = getBulkProcessingEngine();

    // Get job details
    const job = await bulkEngine.getBulkJob(jobId);
    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    // Check if job has failed items to retry
    if (job.results.failed.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No failed items to retry' },
        { status: 400 }
      );
    }

    // Create new bulk job with failed URLs
    const failedUrls = job.results.failed.map(item => ({
      url: item.url,
      providedData: {},
      needsGeneration: {
        name: true,
        description: true,
        features: true,
        pricing: true,
        prosAndCons: true,
        haiku: true,
        hashtags: true,
      },
    }));

    // Ensure all required options are present
    const retryOptions = {
      batchSize: job.processingOptions.batchSize || 5,
      delayBetweenBatches: job.processingOptions.delayBetweenBatches || 2000,
      retryAttempts: job.processingOptions.retryAttempts || 3,
      aiProvider: job.processingOptions.aiProvider || 'openai' as const,
      skipExisting: job.processingOptions.skipExisting || false,
      scrapeOnly: (job.processingOptions as any).scrapeOnly || false,
      generateContent: (job.processingOptions as any).generateContent !== false,
      autoPublish: (job.processingOptions as any).autoPublish || false,
      priority: (job.processingOptions as any).priority || 'normal' as const,
    };

    const retryJob = await bulkEngine.createBulkJob(
      failedUrls,
      retryOptions,
      {
        jobType: job.jobType as 'text_file' | 'json_file' | 'manual_entry',
        filename: `retry_${job.sourceData.filename || 'manual_entry'}`,
        submittedBy: 'admin', // Default since submittedBy might not exist
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        originalJob: job,
        retryJob,
        message: `Created retry job for ${failedUrls.length} failed items`,
      },
    });
  } catch (error) {
    console.error(`Failed to retry bulk processing job ${params.id}:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to create retry job' },
      { status: 500 }
    );
  }
}
