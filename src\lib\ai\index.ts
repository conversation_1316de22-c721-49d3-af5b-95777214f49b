// AI System Main Exports
// Dual AI Provider Setup (OpenAI + OpenRouter) with Intelligent Model Selection

// Main Content Generator
export { AIContentGenerator } from './content-generator';

// Provider Clients
export { OpenAIClient } from './providers/openai-client';
export { OpenRouterClient } from './providers/openrouter-client';

// Core AI System Components
export { ModelSelector } from './model-selector';
export { PromptManager } from './prompt-manager';
export { ContextWindowManager } from './context-window-manager';
export { AIErrorHandler } from './error-handler';

// Types and Interfaces
export type {
  AIProvider,
  AIModel,
  ContentComplexity,
  ProcessingPriority,
  AIModelConfig,
  ModelSelectionCriteria,
  ModelConfig,
  GenerationOptions,
  AIResponse,
  GeneratedContent,
  ValidationResult,
  GenerationResult,
  ErrorContext,
  ErrorResult,
  MultiPromptContext,
  AIProcessingConfig,
  ProcessedContent
} from './types';

// Configuration Constants
export {
  MODEL_OUTPUT_LIMITS,
  OPENAI_CONFIG,
  OPENROUTER_CONFIG
} from './types';

// Utility Functions
export const AIUtils = {
  /**
   * Calculate token count for text (approximate)
   */
  calculateTokenCount: (text: string): number => {
    return Math.ceil(text.length / 4);
  },

  /**
   * Get recommended model for specific use case
   */
  getRecommendedModel: (useCase: 'speed' | 'quality' | 'cost' | 'large_content') => {
    return ModelSelector.getRecommendedModels()[useCase];
  },

  /**
   * Validate AI provider configuration
   */
  validateConfiguration: (): { valid: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!process.env.OPENAI_API_KEY) {
      issues.push('OPENAI_API_KEY environment variable is missing');
    }

    if (!process.env.OPENROUTER_API_KEY) {
      issues.push('OPENROUTER_API_KEY environment variable is missing');
    }

    if (!process.env.SITE_URL) {
      issues.push('SITE_URL environment variable is missing (required for OpenRouter)');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  },

  /**
   * Get provider status
   */
  getProviderStatus: async () => {
    const generator = new AIContentGenerator();
    return await generator.getProviderStatus();
  }
};

// Factory Functions
export const createAIContentGenerator = (options?: {
  preferredProvider?: AIProvider;
  fallbackEnabled?: boolean;
}): AIContentGenerator => {
  return new AIContentGenerator();
};

export const createOpenAIClient = (config?: Partial<AIModelConfig>): OpenAIClient => {
  return new OpenAIClient(config);
};

export const createOpenRouterClient = (config?: Partial<AIModelConfig>): OpenRouterClient => {
  return new OpenRouterClient(config);
};

// Quick Start Functions
export const quickGenerate = async (
  content: string,
  url: string,
  options?: {
    priority?: ProcessingPriority;
    complexity?: ContentComplexity;
  }
): Promise<GenerationResult> => {
  const generator = new AIContentGenerator();
  return await generator.generateContent(content, url, options);
};

export const quickValidateContent = async (content: GeneratedContent): Promise<ValidationResult> => {
  const generator = new AIContentGenerator();
  return await (generator as any).validateGeneratedContent(content);
};

// Error Handling Utilities
export const handleAIError = (error: any, context?: ErrorContext): Promise<ErrorResult> => {
  return AIErrorHandler.handleAIError(error, context || {});
};

export const retryWithFallback = <T>(
  operation: () => Promise<T>,
  maxRetries?: number,
  context?: ErrorContext
): Promise<T> => {
  return AIErrorHandler.retryWithFallback(operation, maxRetries, context);
};

// Configuration Helpers
export const getModelCapabilities = (model: AIModel) => {
  return ModelSelector.getModelCapabilities(model);
};

export const estimateProcessingCost = (
  model: ModelConfig,
  inputTokens: number,
  outputTokens: number
): number => {
  return ModelSelector.estimateCost(model, inputTokens, outputTokens);
};

// Content Processing Utilities
export const optimizeContentForAI = (
  content: string,
  contentQuality?: number,
  scrapingCost?: number
) => {
  return ContextWindowManager.optimizeContentForAI(content, contentQuality, scrapingCost);
};

export const splitContentForModel = (content: string, modelConfig: AIModelConfig): string[] => {
  return ContextWindowManager.splitContentForModel(content, modelConfig);
};

// Prompt Utilities
export const buildPrompt = (content: string, url: string, options?: any) => {
  return PromptManager.buildSinglePrompt(content, url, options);
};

export const extractJsonFromResponse = (response: string) => {
  return PromptManager.extractJsonFromResponse(response);
};

// System Health Check
export const performHealthCheck = async (): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  providers: {
    openai: { status: 'up' | 'down'; latency?: number };
    openrouter: { status: 'up' | 'down'; latency?: number };
  };
  configuration: { valid: boolean; issues: string[] };
}> => {
  const configValidation = AIUtils.validateConfiguration();
  
  try {
    const startTime = Date.now();
    const providerStatus = await AIUtils.getProviderStatus();
    const endTime = Date.now();

    const openaiLatency = providerStatus.openai.available ? endTime - startTime : undefined;
    const openrouterLatency = providerStatus.openrouter.available ? endTime - startTime : undefined;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (!providerStatus.openai.available && !providerStatus.openrouter.available) {
      status = 'unhealthy';
    } else if (!providerStatus.openai.available || !providerStatus.openrouter.available) {
      status = 'degraded';
    }

    return {
      status,
      providers: {
        openai: {
          status: providerStatus.openai.available ? 'up' : 'down',
          latency: openaiLatency
        },
        openrouter: {
          status: providerStatus.openrouter.available ? 'up' : 'down',
          latency: openrouterLatency
        }
      },
      configuration: configValidation
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      providers: {
        openai: { status: 'down' },
        openrouter: { status: 'down' }
      },
      configuration: configValidation
    };
  }
};

// Version and Info
export const AI_SYSTEM_INFO = {
  version: '1.0.0',
  providers: ['OpenAI', 'OpenRouter'],
  models: {
    openai: ['gpt-4o-2024-11-20', 'gpt-4o', 'gpt-4o-mini'],
    openrouter: ['google/gemini-2.5-pro-preview', 'google/gemini-pro-1.5', 'anthropic/claude-3.5-sonnet']
  },
  features: [
    'Dual Provider Support',
    'Intelligent Model Selection',
    'Context Window Management',
    'Multi-Prompt Processing',
    'Automatic Fallback',
    'Error Recovery',
    'Cost Optimization',
    'Quality-First Processing'
  ]
};
