import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { 
  AIContentGenerator, 
  ModelSelector,
  createOpenAIClient,
  createOpenRouterClient,
  GenerationOptions 
} from '@/lib/ai';

export async function POST(request: NextRequest) {
  try {
    // Validate admin API key for security
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { 
      testType = 'full',
      forceProvider = null,
      testContent = null 
    } = await request.json();

    console.log(`🧪 Starting AI Content Generation Test (${testType})`);

    const results = {
      timestamp: new Date().toISOString(),
      testType,
      tests: []
    };

    // Test content for AI generation
    const defaultTestContent = `
# Test AI Tool: AI Content Generator Pro

**URL:** https://example-ai-tool.com

**Title:** AI Content Generator Pro - Advanced Content Creation Tool

**Description:** A powerful AI-powered content generation platform that helps businesses create high-quality content at scale.

**Main Content:**
AI Content Generator Pro is a cutting-edge platform designed for modern content creators, marketers, and businesses. 

Key capabilities include:
- Advanced natural language processing
- Multi-format content generation (blog posts, social media, emails)
- SEO optimization features
- Brand voice customization
- Real-time collaboration tools
- Analytics and performance tracking

The platform integrates with popular CMS platforms and marketing tools, making it easy to incorporate into existing workflows. With support for 25+ languages and industry-specific templates, it's suitable for businesses of all sizes.

**Pricing Information:**
- Starter Plan: $29/month - 50,000 words, basic templates
- Professional Plan: $79/month - 200,000 words, advanced features, API access
- Enterprise Plan: Custom pricing - Unlimited words, white-label options, dedicated support

**Features:**
- AI-powered content generation
- SEO optimization tools
- Multi-language support
- Brand voice training
- Collaboration features
- API integration
- Analytics dashboard
- Template library
`;

    const testContentToUse = testContent || defaultTestContent;

    // Test 1: Model Selection
    if (testType === 'full' || testType === 'selection') {
      console.log('Testing model selection...');
      const selectionTests = [
        { contentSize: 5000, complexity: 'simple' as const, priority: 'speed' as const },
        { contentSize: 50000, complexity: 'medium' as const, priority: 'quality' as const },
        { contentSize: 200000, complexity: 'complex' as const, priority: 'quality' as const }
      ];

      const selectionResults = selectionTests.map(criteria => {
        const selected = ModelSelector.selectOptimalModel(criteria);
        return {
          criteria,
          selected: {
            provider: selected.provider,
            model: selected.model,
            reasoning: selected.reasoning
          }
        };
      });

      results.tests.push({
        name: 'Model Selection',
        status: 'completed',
        results: selectionResults
      });
    }

    // Test 2: Individual Provider Tests
    if (testType === 'full' || testType === 'providers') {
      console.log('Testing individual providers...');
      
      const providerResults = [];

      // Test OpenAI if available and not forced to specific provider
      if (process.env.OPENAI_API_KEY && (!forceProvider || forceProvider === 'openai')) {
        try {
          console.log('Testing OpenAI provider...');
          const startTime = Date.now();
          
          const openaiClient = createOpenAIClient();
          const response = await openaiClient.generateContent(
            'You are a helpful AI assistant. Generate a brief JSON response about the test tool.',
            `Generate basic information about this AI tool in JSON format:\n${testContentToUse.substring(0, 1000)}`,
            {
              model: 'gpt-4o-2024-11-20',
              responseFormat: 'json_object',
              maxTokens: 500
            }
          );

          const duration = Date.now() - startTime;
          
          providerResults.push({
            provider: 'OpenAI',
            model: 'gpt-4o-2024-11-20',
            status: 'success',
            duration,
            tokenUsage: response.tokenUsage,
            contentLength: response.content.length,
            finishReason: response.finishReason
          });

          console.log(`OpenAI test completed in ${duration}ms`);
        } catch (error: any) {
          providerResults.push({
            provider: 'OpenAI',
            status: 'failed',
            error: error.message
          });
          console.error('OpenAI test failed:', error.message);
        }
      }

      // Test OpenRouter if available and not forced to specific provider
      if (process.env.OPENROUTER_API_KEY && (!forceProvider || forceProvider === 'openrouter')) {
        try {
          console.log('Testing OpenRouter provider...');
          const startTime = Date.now();
          
          const openrouterClient = createOpenRouterClient();
          const response = await openrouterClient.generateContent(
            'You are a helpful AI assistant. Generate a brief JSON response about the test tool.',
            `Generate basic information about this AI tool in JSON format:\n${testContentToUse.substring(0, 1000)}`,
            {
              model: 'google/gemini-2.5-pro-preview',
              responseFormat: 'json_object',
              maxTokens: 500
            }
          );

          const duration = Date.now() - startTime;
          
          providerResults.push({
            provider: 'OpenRouter',
            model: 'google/gemini-2.5-pro-preview',
            status: 'success',
            duration,
            tokenUsage: response.tokenUsage,
            contentLength: response.content.length,
            finishReason: response.finishReason
          });

          console.log(`OpenRouter test completed in ${duration}ms`);
        } catch (error: any) {
          providerResults.push({
            provider: 'OpenRouter',
            status: 'failed',
            error: error.message
          });
          console.error('OpenRouter test failed:', error.message);
        }
      }

      results.tests.push({
        name: 'Provider Tests',
        status: 'completed',
        results: providerResults
      });
    }

    // Test 3: Full Content Generation with Fallback
    if (testType === 'full' || testType === 'generation') {
      console.log('Testing full content generation...');
      
      try {
        const generator = new AIContentGenerator();
        const startTime = Date.now();
        
        const options: GenerationOptions = {
          complexity: 'medium',
          priority: 'quality',
          maxRetries: 2
        };

        const result = await generator.generateContent(
          testContentToUse,
          'https://example-ai-tool.com',
          options
        );

        const duration = Date.now() - startTime;

        results.tests.push({
          name: 'Full Content Generation',
          status: result.success ? 'success' : 'failed',
          duration,
          result: {
            success: result.success,
            modelUsed: result.modelUsed,
            tokenUsage: result.tokenUsage,
            validationScore: result.validation?.score,
            strategyUsed: result.strategyUsed,
            contentSample: result.content?.detailed_description?.substring(0, 200) + '...',
            featuresCount: result.content?.features?.length,
            error: result.error
          }
        });

        console.log(`Content generation completed in ${duration}ms`);
      } catch (error: any) {
        results.tests.push({
          name: 'Full Content Generation',
          status: 'failed',
          error: error.message
        });
        console.error('Content generation test failed:', error.message);
      }
    }

    // Test 4: Fallback Mechanism (simulate failure)
    if (testType === 'full' || testType === 'fallback') {
      console.log('Testing fallback mechanism...');
      
      try {
        // This test would require more complex setup to simulate failures
        // For now, we'll test the fallback logic conceptually
        const fallbackTest = ModelSelector.getFallbackModel(
          { provider: 'openai', model: 'gpt-4o-2024-11-20', maxTokens: 128000, reasoning: 'Primary' },
          'Simulated failure for testing'
        );

        results.tests.push({
          name: 'Fallback Mechanism',
          status: 'success',
          result: {
            fallbackProvider: fallbackTest.provider,
            fallbackModel: fallbackTest.model,
            reasoning: fallbackTest.reasoning
          }
        });
      } catch (error: any) {
        results.tests.push({
          name: 'Fallback Mechanism',
          status: 'failed',
          error: error.message
        });
      }
    }

    // Calculate overall test status
    const failedTests = results.tests.filter(test => test.status === 'failed');
    const overallStatus = failedTests.length === 0 ? 'success' : 'partial';

    return NextResponse.json({
      success: true,
      overall: overallStatus,
      summary: {
        totalTests: results.tests.length,
        passed: results.tests.filter(test => test.status === 'success').length,
        failed: failedTests.length,
        duration: Date.now() - new Date(results.timestamp).getTime()
      },
      ...results
    });

  } catch (error: any) {
    console.error('AI test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
