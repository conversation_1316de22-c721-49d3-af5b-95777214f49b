import { Job, <PERSON>Handler, ContentGenerationJobData } from '../types';
import { ContentGenerationPipeline } from '../../content-generation/pipeline';
import { ContentGenerationRequest } from '../../types';

export class ContentGenerationHandler implements JobHandler {
  private pipeline: ContentGenerationPipeline;

  constructor() {
    this.pipeline = new ContentGenerationPipeline();
  }

  async handle(job: Job): Promise<any> {
    const data = job.data as ContentGenerationJobData;

    if (!process.env.CONTENT_GENERATION_ENABLED || process.env.CONTENT_GENERATION_ENABLED !== 'true') {
      throw new Error('Content generation is disabled');
    }

    try {
      // Prepare content for AI generation
      const scrapedContent = this.prepareScrapedContent(data);

      // Build content generation request
      const request: ContentGenerationRequest = {
        toolId: data.toolId || job.id, // Use job ID if toolId not provided
        scrapedContent,
        toolUrl: data.url,
        complexity: data.complexity || 'medium',
        priority: data.priority || 'quality',
        contentQuality: data.contentQuality || 70,
        scrapingCost: data.scrapingCost || 0
      };

      // Pipeline options
      const pipelinePriority: 'high' | 'normal' | 'low' =
        data.priority === 'quality' ? 'high' :
        data.priority === 'speed' ? 'low' : 'normal';

      const options = {
        skipValidation: false,
        requireEditorialReview: data.requireEditorialReview || false,
        autoApprove: data.autoApprove || false,
        qualityThreshold: data.qualityThreshold || 80,
        priority: pipelinePriority
      };

      console.log(`Starting content generation pipeline for: ${data.url}`);
      console.log(`Tool ID: ${request.toolId}`);
      console.log(`Content size: ${scrapedContent.length} characters`);
      console.log(`Options:`, options);

      // Execute pipeline
      const result = await this.pipeline.execute(request, options);

      if (!result.success) {
        throw new Error(`Content generation pipeline failed: ${result.error}`);
      }

      console.log(`Pipeline completed successfully for tool: ${request.toolId}`);
      console.log(`Status: ${result.status}, Workflow: ${result.workflowState}`);
      console.log(`Quality Score: ${result.qualityScore?.overall || 'N/A'}`);

      return {
        success: true,
        toolId: result.toolId,
        status: result.status,
        workflowState: result.workflowState,
        content: result.generatedContent,
        validation: result.validation,
        qualityScore: result.qualityScore,
        editorialReview: result.editorialReview,
        metadata: result.metadata
      };
    } catch (error) {
      console.error('Content generation pipeline failed:', error);
      throw error;
    }
  }

  /**
   * Prepare scraped content for AI processing
   */
  private prepareScrapedContent(data: ContentGenerationJobData): string {
    const { url, scrapedData, pricingData, faqData } = data;

    if (!scrapedData) {
      throw new Error('No scraped data available for content generation');
    }

    // Build comprehensive content string for AI processing
    let content = `# AI Tool Analysis\n\n`;
    content += `**URL:** ${url}\n\n`;

    if (scrapedData.title) {
      content += `**Title:** ${scrapedData.title}\n\n`;
    }

    if (scrapedData.description) {
      content += `**Description:** ${scrapedData.description}\n\n`;
    }

    if (scrapedData.textContent) {
      content += `**Main Content:**\n${scrapedData.textContent}\n\n`;
    }

    if (scrapedData.pricingText) {
      content += `**Pricing Information:**\n${scrapedData.pricingText}\n\n`;
    }

    if (scrapedData.faqText) {
      content += `**FAQ Content:**\n${scrapedData.faqText}\n\n`;
    }

    // Add structured pricing data if available
    if (pricingData) {
      content += `**Structured Pricing Data:**\n${JSON.stringify(pricingData, null, 2)}\n\n`;
    }

    // Add structured FAQ data if available
    if (faqData) {
      content += `**Structured FAQ Data:**\n${JSON.stringify(faqData, null, 2)}\n\n`;
    }

    // Add any additional scraped data
    if (scrapedData.features && scrapedData.features.length > 0) {
      content += `**Extracted Features:**\n${scrapedData.features.join('\n')}\n\n`;
    }

    if (scrapedData.socialLinks) {
      content += `**Social Links:**\n`;
      Object.entries(scrapedData.socialLinks).forEach(([platform, url]) => {
        if (url) content += `- ${platform}: ${url}\n`;
      });
      content += '\n';
    }

    return content;
  }

}
